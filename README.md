# Linux 文件整理器

这是一个智能的 Linux 文件整理工具，包含三个主要功能：

1. **文件整理** - 根据文件名中的模特名称自动整理文件到对应的文件夹中
2. **批量解压** - 批量解压压缩文件并自动删除原始压缩包
3. **文件后缀重命名** - 将指定目录二级子目录中的非压缩、非媒体文件后缀修改为7z

## 功能特点

### 文件整理功能 (smart_file_organizer.py)

- 🔍 智能识别文件名中的模特名称
- 📁 自动创建模特名称对应的文件夹
- 📊 详细的整理报告和统计信息
- 🔙 自动生成回滚脚本，支持撤销操作
- 🧪 支持试运行模式，预览整理结果

### 批量解压功能 (unzip_and_clean.py)

- 📦 支持 .zip, .rar, .7z 格式
- 🔐 支持密码保护的压缩文件
- 🔄 自动覆盖同名文件
- 🗑️ 解压成功后自动删除原始压缩包
- 📁 递归扫描所有子目录

### 文件后缀重命名功能 (rename_to_7z.py)

- 🔄 将无扩展名文件添加.7z后缀
- 📂 只处理指定目录的二级子目录
- 🛡️ 自动跳过所有有扩展名的文件
- 🧪 支持试运行模式，预览修改结果
- 📊 详细的处理统计信息

## 安装要求

- Python 3.6+
- Linux 操作系统
- 系统工具：`7z`, `unrar`

### 安装系统依赖

```bash
sudo apt update
sudo apt install p7zip-full unrar
```

## 使用方法

### 1. 文件整理功能

#### 基本用法

```bash
python3 smart_file_organizer.py [源目录] [目标目录]
```

#### 参数说明

- `源目录`: 包含需要整理文件的目录
- `目标目录`: 整理后文件的存放目录
- `--dry-run`: 试运行模式，只显示会进行的操作，不实际移动文件
- `--verbose`: 详细输出模式

#### 使用示例

1. **试运行模式**（推荐先使用）：

```bash
python3 smart_file_organizer.py /vol2/1000/vip套图 /vol2/1000/vip套图/organized --dry-run
```

2. **实际执行**：

```bash
python3 smart_file_organizer.py /vol2/1000/vip套图 /vol2/1000/vip套图/organized
```

3. **详细输出**：

```bash
python3 smart_file_organizer.py /vol2/1000/vip套图 /vol2/1000/vip套图/organized --verbose
```

#### 文件命名规则

程序会识别以下格式的文件名：

- `YYYYMMDD_HHMMSS_模特名_其他信息.扩展名`
- 例如：`20240315_143022_小美_写真集.jpg`

从文件名中提取的模特名称将用作文件夹名称。

#### 输出结构

整理后的文件结构如下：

```
目标目录/
├── 小美/
│   ├── 20240315_143022_小美_写真集.jpg
│   └── 20240316_120000_小美_生活照.mp4
├── 小红/
│   ├── 20240317_090000_小红_旅游照.jpg
│   └── 20240318_150000_小红_工作照.png
└── ...
```

#### 回滚操作

如果需要撤销整理操作，可以运行自动生成的回滚脚本：

```bash
bash smart_rollback_YYYYMMDD_HHMMSS.sh
```

### 2. 批量解压功能

#### 基本用法

```bash
python3 unzip_and_clean.py [目录路径]
```

#### 参数说明

- `目录路径`: 包含压缩文件的目录（会递归扫描所有子目录）
- `-p, --password`: 解压密码（可选）

#### 使用示例

1. **无密码解压**：

```bash
python3 unzip_and_clean.py /vol2/1000/vip套图/organized
```

2. **带密码解压**：

```bash
python3 unzip_and_clean.py /vol2/1000/vip套图/organized -p "TG@ioopro"
```

3. **指定特定文件夹**：

```bash
python3 unzip_and_clean.py /vol2/1000/vip套图/雪顶 -p "your_password"
```

#### 解压行为

- 每个压缩包会解压到与压缩包同名的文件夹中
- 如果遇到同名文件，会自动覆盖
- 解压成功后，原始压缩包会被删除
- 保持压缩包内的原始目录结构

### 3. 文件后缀重命名功能

#### 基本用法

```bash
python3 rename_to_7z.py [目录路径]
```

#### 参数说明

- `目录路径`: 要处理的根目录路径
- `--dry-run`: 试运行模式，只显示会进行的操作，不实际重命名文件

#### 使用示例

1. **试运行模式**（推荐先使用）：

```bash
python3 rename_to_7z.py /vol2/1000/vip套图/整合 --dry-run
```

2. **实际执行**：

```bash
python3 rename_to_7z.py /vol2/1000/vip套图/整合
```

#### 处理规则

- 只处理指定目录的**二级子目录**中的文件
- 例如：`/vol2/1000/vip套图/整合/喝多了想兔/` 中的文件会被处理
- 只处理没有扩展名的文件
- 跳过所有有扩展名的文件（包括图片、视频、音频、压缩文件等）

#### 处理示例

假设目录结构如下：

```
/vol2/1000/vip套图/整合/
├── 喝多了想兔/
│   ├── 无扩展名文件1    → 重命名为 无扩展名文件1.7z
│   ├── 无扩展名文件2    → 重命名为 无扩展名文件2.7z
│   ├── photo1.txt      → 跳过（有扩展名）
│   ├── image.jpg       → 跳过（有扩展名）
│   └── video.mp4       → 跳过（有扩展名）
└── 其他模特/
    ├── 数据文件         → 重命名为 数据文件.7z
    └── archive.zip     → 跳过（有扩展名）
```

## 完整工作流程示例

假设你有一个包含压缩文件的目录，想要先解压再整理：

```bash
# 1. 先解压所有压缩文件
python3 unzip_and_clean.py /vol2/1000/vip套图 -p "TG@ioopro"

# 2. 再整理解压后的文件
python3 smart_file_organizer.py /vol2/1000/vip套图 /vol2/1000/vip套图/organized --dry-run

# 3. 确认无误后执行实际整理
python3 smart_file_organizer.py /vol2/1000/vip套图 /vol2/1000/vip套图/organized
```

## 注意事项

- 建议先使用 `--dry-run` 模式预览整理结果
- 解压功能会删除原始压缩包，请确保备份重要数据
- 程序会自动处理文件名冲突
- 整理过程中会生成详细的日志文件
- 支持中文文件名和路径

## 故障排除

如果遇到问题，请检查：

1. Python 版本是否为 3.6+
2. 是否已安装 `7z` 和 `unrar` 工具
3. 源目录和目标目录路径是否正确
4. 是否有足够的磁盘空间
5. 是否有相应的文件读写权限
6. 压缩文件是否需要密码

## 许可证

MIT License
