#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
批量解压脚本
- 支持 .zip, .rar, .7z 格式
- 支持密码解压
- 成功解压后删除原文件
"""

import os
import sys
import argparse
import subprocess


def unzip_and_clean(directory, password=None):
    """解压目录中的所有压缩文件并清理"""
    print(f"📂 正在扫描目录: {directory}")
    supported_formats = ['.zip', '.rar', '.7z']

    files_to_process = []
    for root, dirs, files in os.walk(directory):
        for item in files:
            if item.endswith(tuple(supported_formats)):
                files_to_process.append(os.path.join(root, item))

    print(f"🔍 找到 {len(files_to_process)} 个压缩文件，开始处理...")

    for file_path in files_to_process:
        item = os.path.basename(file_path)
        print(f"\n---\n🔄 正在处理: {item}")

        parent_dir = os.path.dirname(file_path)
        archive_basename = os.path.basename(os.path.splitext(file_path)[0])
        safe_basename = archive_basename[:100]
        final_extract_path = os.path.join(parent_dir, safe_basename)
        os.makedirs(final_extract_path, exist_ok=True)

        try:
            # 1. List files in the archive to get their names
            list_command = ['7z', 'l', file_path]
            if password:
                list_command.append(f'-p{password}')

            list_result = subprocess.run(list_command, capture_output=True, text=True, errors='ignore')

            # Robustly parse the file list from 7z output
            lines = list_result.stdout.splitlines()
            separator_start = '-------------------'
            start_index = -1
            end_index = -1

            for i, line in enumerate(lines):
                if line.strip().startswith(separator_start):
                    if start_index == -1:
                        start_index = i + 1
                    else:
                        end_index = i
                        break

            if start_index != -1 and end_index != -1:
                file_lines = lines[start_index:end_index]
                # Extract filenames, ignoring directory entries (lines with 'D' attribute)
                filenames = [line[53:].strip() for line in file_lines if 'D' not in line[20:25]]
            else:
                filenames = []

            if not filenames:
                raise Exception("Could not list files in archive or archive is empty. Check for password protection or corruption.")

            # 2. Build the correct command based on file type, with overwrite enabled
            if item.endswith('.rar'):
                # Use 'unrar' with -o+ to force overwrite
                extract_command = ['unrar', 'x', '-o+', file_path, final_extract_path]
                if password:
                    extract_command.insert(3, f'-p{password}') # unrar password syntax
            else:
                # Use '7z' with -aoa to force overwrite
                extract_command = ['7z', 'x', file_path, f'-o{final_extract_path}', '-aoa']
                if password:
                    extract_command.append(f'-p{password}')

            subprocess.run(extract_command, capture_output=True, check=True)



            print(f"✅ 成功解压: {item}")
            os.remove(file_path)
            print(f"🗑️ 已删除原始文件: {item}")

        except subprocess.CalledProcessError as e:
            print(f"❌ 解压失败: {item}")
            error_message = e.stderr.decode('utf-8', 'ignore').strip()
            print(f"   错误信息: {error_message}")
            if os.path.exists(final_extract_path) and not os.listdir(final_extract_path):
                os.rmdir(final_extract_path)
        except Exception as e:
            print(f"❌ 发生意外错误: {item} - {e}")

def main():
    parser = argparse.ArgumentParser(description="批量解压并清理压缩文件")
    parser.add_argument('directory', help='要处理的目录路径')
    parser.add_argument('-p', '--password', help='可选的解压密码', default=None)
    args = parser.parse_args()

    if not os.path.isdir(args.directory):
        print(f"❌ 错误: 目录不存在 - {args.directory}")
        sys.exit(1)

    unzip_and_clean(args.directory, args.password)
    print("\n🎉 所有操作完成!")

if __name__ == "__main__":
    main()

