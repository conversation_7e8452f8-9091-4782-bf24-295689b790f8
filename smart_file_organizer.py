#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
智能文件整理器 - 基于相似度算法的文件整理工具
使用优化后的模特名称相似度算法进行智能合并和文件整理
"""

import os
import sys
import shutil
import json
import re
from pathlib import Path
from collections import defaultdict
from datetime import datetime
import logging

# 添加当前目录到Python路径
current_dir = Path(__file__).parent.resolve()
sys.path.insert(0, str(current_dir))




class SmartFileOrganizer:
    def __init__(self, source_dir, target_dir, log_level=logging.INFO):
        """初始化智能文件整理器"""
        self.source_dir = Path(source_dir).resolve()
        self.target_dir = Path(target_dir).resolve()
        self.setup_logging(log_level)
        
        # 初始化组件
        # self.classifier = ModelNameClassifier()
        # self.analyzer = SimilarityAnalyzer()
        
        # 统计信息
        self.stats = {
            'total_files': 0,
            'total_folders': 0,
            'organized_files': 0,
            'organized_folders': 0,
            'created_folders': 0,
            'merged_groups': 0,
            'unidentified_items': 0,
            'errors': 0
        }
        
        # 操作记录
        self.operations = []
        self.model_groups = {}  # 模特名称组合映射
        self.folder_mapping = {}  # 文件夹映射关系
        
    def setup_logging(self, log_level):
        """设置日志"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        log_filename = Path.cwd() / f'smart_organizer_{timestamp}.log'
        
        logging.basicConfig(
            level=log_level,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(log_filename, encoding='utf-8'),
                logging.StreamHandler(sys.stdout)
            ]
        )
        self.logger = logging.getLogger(__name__)
        self.logger.info(f"智能文件整理器日志: {log_filename}")
        self.log_filename = log_filename
    
    def scan_source_directory(self):
        """扫描源目录，收集所有文件和文件夹信息"""
        self.logger.info(f"📂 扫描源目录: {self.source_dir}")

        if not self.source_dir.exists():
            raise FileNotFoundError(f"源目录不存在: {self.source_dir}")

        files = []
        for item in self.source_dir.glob('*'):
            # 跳过目标目录及其子目录
            try:
                if self.target_dir.exists() and (item == self.target_dir or self.target_dir in item.parents):
                    continue
            except (OSError, ValueError):
                # 如果路径比较出错，继续处理
                pass

            if item.is_file():
                # 跳过隐藏文件和系统文件
                if not item.name.startswith('.') and item.name != 'Thumbs.db':
                    files.append({
                        'path': str(item),
                        'name': item.name,
                        'parent': str(item.parent),
                        'size': item.stat().st_size,
                        'type': 'file'
                    })

        self.stats['total_files'] = len(files)
        self.stats['total_folders'] = 0

        self.logger.info(f"✅ 扫描完成: {len(files)} 个文件")
        return files, []
    
    def _extract_model_name_simple(self, name):
        """根据 '时间戳_模特名 描述.后缀' 格式提取模特名"""
        # 移除文件扩展名
        name_without_ext = Path(name).stem
        # 匹配时间戳格式 YYYYMMDD_HHMMSS_
        match = re.match(r'(\d{8}_\d{6}_)(.*)', name_without_ext)
        if match:
            # 获取时间戳之后的所有内容
            remaining_str = match.group(2)
            # 模特名是开头到第一个特殊字符（非字母、数字、汉字）的部分
            # 我们只匹配字母、数字和中文字符
            model_name_match = re.match(r'([a-zA-Z0-9\u4e00-\u9fa5]+)', remaining_str)
            if model_name_match:
                return model_name_match.group(1)
        return '未识别'

    def extract_model_names(self, files):
        """提取所有模特名称"""
        self.logger.info("🔍 提取模特名称...")

        all_model_names = set()
        item_to_model = {}  # 项目到模特名称的映射

        for item_info in files:
            model_name = self._extract_model_name_simple(item_info['name'])
            if model_name and model_name != '未识别':
                all_model_names.add(model_name)
                item_to_model[item_info['path']] = model_name

        self.logger.info(f"✅ 提取到 {len(all_model_names)} 个唯一模特名称")
        # Since we are not using similarity groups, we can directly use the extracted names
        self.model_groups = {name: name for name in all_model_names}
        return all_model_names, item_to_model

    def create_target_folders(self):
        """创建目标文件夹结构"""
        self.logger.info("📁 创建目标文件夹结构...")
        
        # 确保目标目录存在
        self.target_dir.mkdir(parents=True, exist_ok=True)
        
        # 获取所有标准模特名称
        canonical_names = set(self.model_groups.values())
        
        created_count = 0
        for canonical_name in canonical_names:
            if canonical_name and canonical_name not in ['未识别', '需要分析']:
                # 清理文件夹名称，确保Linux兼容
                folder_name = self.sanitize_folder_name(canonical_name)
                folder_path = self.target_dir / folder_name
                
                if not folder_path.exists():
                    try:
                        folder_path.mkdir(parents=True, exist_ok=True)
                        
                        # 创建标识文件
                        marker_file = folder_path / '.file_organizer_marker'
                        with open(marker_file, 'w', encoding='utf-8') as f:
                            f.write(f"Created by Smart File Organizer\n")
                            f.write(f"Canonical name: {canonical_name}\n")
                            f.write(f"Created at: {datetime.now()}\n")
                        
                        self.folder_mapping[canonical_name] = str(folder_path)
                        created_count += 1
                        
                        self.operations.append(f"创建文件夹: {folder_path}")
                        
                    except Exception as e:
                        self.logger.error(f"创建文件夹失败 {folder_path}: {e}")
                        self.stats['errors'] += 1
                else:
                    self.folder_mapping[canonical_name] = str(folder_path)
        
        self.stats['created_folders'] = created_count
        self.logger.info(f"✅ 创建了 {created_count} 个文件夹")
    
    def sanitize_folder_name(self, name):
        """清理文件夹名称，确保Linux兼容性"""
        if not name:
            return "未识别"
        
        # 移除或替换Linux不兼容的字符
        sanitized = name
        
        # 替换危险字符
        dangerous_chars = ['/', '\\', ':', '*', '?', '"', '<', '>', '|']
        for char in dangerous_chars:
            sanitized = sanitized.replace(char, '_')
        
        # 移除控制字符
        sanitized = ''.join(char for char in sanitized if ord(char) >= 32)
        
        # 移除首尾空格和点
        sanitized = sanitized.strip(' .')
        
        # 确保不为空
        if not sanitized:
            sanitized = "未识别"
        
        # 限制长度（Linux文件名限制255字节）
        if len(sanitized.encode('utf-8')) > 200:  # 留一些余量
            sanitized = sanitized[:100] + "..."
        
        return sanitized
    
    def organize_files(self, files, item_to_model):
        """整理文件"""
        self.logger.info("📄 开始整理文件...")
        
        organized_count = 0
        unidentified_count = 0
        
        for file_info in files:
            file_path = Path(file_info['path'])
            
            if not file_path.exists():
                continue
            
            # 获取模特名称
            model_name = item_to_model.get(file_info['path'])
            
            if model_name and model_name in self.model_groups:
                canonical_name = self.model_groups[model_name]
                target_folder = self.folder_mapping.get(canonical_name)
                
                if target_folder:
                    target_path = Path(target_folder) / file_path.name
                    
                    # 处理文件名冲突
                    if target_path.exists():
                        target_path = self.resolve_name_conflict(target_path)
                    
                    try:
                        shutil.move(str(file_path), str(target_path))
                        organized_count += 1
                        
                        self.operations.append(f"移动文件: {file_path} -> {target_path}")
                        
                        if organized_count % 100 == 0:
                            self.logger.info(f"   已整理 {organized_count} 个文件...")
                        
                    except Exception as e:
                        self.logger.error(f"移动文件失败 {file_path} -> {target_path}: {e}")
                        self.stats['errors'] += 1
                else:
                    unidentified_count += 1
                    self.logger.warning(f"未识别文件 (无法找到目标文件夹): {file_path.name}")
            else:
                unidentified_count += 1
                self.logger.warning(f"未识别文件 (无法提取模特名): {file_path.name}")
        
        self.stats['organized_files'] = organized_count
        self.stats['unidentified_items'] += unidentified_count
        
        self.logger.info(f"✅ 文件整理完成: {organized_count} 个已整理, {unidentified_count} 个未识别")
    

    
    def resolve_name_conflict(self, target_path):
        """解决文件名冲突"""
        base_path = target_path.parent
        base_name = target_path.stem
        extension = target_path.suffix
        
        counter = 1
        while target_path.exists():
            new_name = f"{base_name}_{counter}{extension}"
            target_path = base_path / new_name
            counter += 1
        
        return target_path

    def save_organize_report(self, output_file):
        """保存整理报告"""
        try:
            report = {
                'timestamp': datetime.now().isoformat(),
                'source_directory': str(self.source_dir),
                'target_directory': str(self.target_dir),
                'statistics': self.stats,
                'similarity_groups': 0,  # 已移除
                'model_groups_count': len(set(self.model_groups.values())),
                'operations': self.operations[:1000],  # 限制操作记录数量
                'folder_mapping': self.folder_mapping
            }

            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(report, f, ensure_ascii=False, indent=2)

            self.logger.info(f"整理报告已保存到: {output_file}")
            return True

        except Exception as e:
            self.logger.error(f"保存整理报告时出错: {e}")
            return False

    def create_rollback_script(self, script_file):
        """创建回滚脚本"""
        try:
            with open(script_file, 'w', encoding='utf-8') as f:
                f.write("#!/bin/bash\n")
                f.write("# 智能文件整理器回滚脚本\n")
                f.write(f"# 生成时间: {datetime.now()}\n")
                f.write(f"# 源目录: {self.source_dir}\n")
                f.write(f"# 目标目录: {self.target_dir}\n\n")

                f.write("echo '开始回滚智能文件整理操作...'\n\n")

                # 生成回滚命令
                rollback_count = 0
                for operation in reversed(self.operations):
                    if operation.startswith("移动文件:"):
                        # 解析移动操作
                        parts = operation.split(" -> ")
                        if len(parts) == 2:
                            source = parts[0].replace("移动文件: ", "")
                            target = parts[1]

                            # 添加安全检查
                            f.write(f'# 回滚文件: {Path(target).name}\n')
                            f.write(f'if [ -f "{target}" ]; then\n')
                            f.write(f'    # 确保目标目录存在\n')
                            f.write(f'    mkdir -p "$(dirname "{source}")"\n')
                            f.write(f'    mv "{target}" "{source}"\n')
                            f.write(f'    echo "已回滚: {Path(target).name} -> {Path(source).parent.name}/{Path(source).name}"\n')
                            f.write(f'else\n')
                            f.write(f'    echo "警告: 文件不存在 {target}"\n')
                            f.write(f'fi\n\n')
                            rollback_count += 1

                    elif operation.startswith("整理文件夹:"):
                        # 文件夹整理的回滚比较复杂，记录但不自动回滚
                        f.write(f'# {operation}\n')

                f.write(f'echo "已处理 {rollback_count} 个文件的回滚操作"\n\n')

                # 删除程序创建的文件夹
                f.write(f'# 删除程序创建的空文件夹\n')
                f.write(f'echo "清理空文件夹..."\n')
                f.write(f'find "{self.target_dir}" -name ".file_organizer_marker" -type f | while read marker; do\n')
                f.write(f'    folder=$(dirname "$marker")\n')
                f.write(f'    echo "检查文件夹: $folder"\n')
                f.write(f'    if [ -z "$(find "$folder" -type f ! -name ".file_organizer_marker")" ]; then\n')
                f.write(f'        echo "删除空文件夹: $folder"\n')
                f.write(f'        rm -f "$marker"\n')
                f.write(f'        rmdir "$folder" 2>/dev/null\n')
                f.write(f'    fi\n')
                f.write(f'done\n\n')

                f.write("echo '回滚完成！所有文件已恢复到原始位置。'\n")

            # 设置脚本可执行权限
            try:
                os.chmod(script_file, 0o755)
            except OSError:
                # Windows环境下可能无法设置权限，忽略错误
                pass

            self.logger.info(f"回滚脚本已创建: {script_file}")
            return True

        except Exception as e:
            self.logger.error(f"创建回滚脚本时出错: {e}")
            return False

    def run(self, dry_run=False):
        """运行智能文件整理"""
        mode_text = "试运行" if dry_run else "实际执行"
        self.logger.info(f"🚀 开始智能文件整理 - {mode_text}")

        try:
            # 1. 扫描源目录
            files, folders = self.scan_source_directory()

            if not files:
                self.logger.warning("没有找到需要整理的文件")
                return False

            # 2. 提取模特名称
            all_model_names, item_to_model = self.extract_model_names(files)

            if not all_model_names:
                self.logger.warning("没有识别到任何模特名称")
                return False

            # 3. 模特名称已在extract_model_names中处理
            model_to_canonical = self.model_groups

            # 4. 创建目标文件夹结构
            if not dry_run:
                self.create_target_folders()
            else:
                self.logger.info(f"[试运行] 将创建 {len(set(model_to_canonical.values()))} 个文件夹")

            # 5. 整理文件
            if not dry_run:
                self.organize_files(files, item_to_model)

            else:
                # 试运行模式：只统计
                identified_files = sum(1 for f in files if item_to_model.get(f['path']) in model_to_canonical)
                self.logger.info(f"[试运行] 将整理 {identified_files} 个文件")
                self.logger.info(f"[试运行] 未识别项目: {len(files) - identified_files}")

            # 6. 生成报告和回滚脚本
            if not dry_run:
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

                # 保存整理报告
                report_file = Path.cwd() / f"smart_organize_report_{timestamp}.json"
                self.save_organize_report(report_file)

                # 创建回滚脚本
                rollback_script = Path.cwd() / f"smart_rollback_{timestamp}.sh"
                self.create_rollback_script(rollback_script)

            # 7. 显示统计信息
            self.print_summary(dry_run)

            return True

        except Exception as e:
            self.logger.error(f"智能文件整理失败: {e}")
            import traceback
            traceback.print_exc()
            return False

    def print_summary(self, dry_run=False):
        """打印整理摘要"""
        mode_text = "试运行" if dry_run else "实际执行"

        print(f"\n📊 智能文件整理摘要 - {mode_text}")
        print("=" * 60)
        print(f"源目录: {self.source_dir}")
        print(f"目标目录: {self.target_dir}")
        print(f"总文件数: {self.stats['total_files']:,}")
        if not dry_run:
            print(f"已整理文件: {self.stats['organized_files']:,}")
            print(f"创建文件夹: {self.stats['created_folders']:,}")
            print(f"相似组合并: {self.stats['merged_groups']:,}")
            print(f"未识别项目: {self.stats['unidentified_items']:,}")
            print(f"错误数量: {self.stats['errors']:,}")

        print(f"日志文件: {self.log_filename}")


def main():
    """主函数"""
    import argparse

    parser = argparse.ArgumentParser(
        description="智能文件整理器 - 基于相似度算法的文件整理工具",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  # 试运行模式
  python3 smart_file_organizer.py /vol2/1000/vip套图 /vol2/1000/organized --dry-run

  # 实际执行
  python3 smart_file_organizer.py /vol2/1000/vip套图 /vol2/1000/organized

  # 详细输出模式
  python3 smart_file_organizer.py /vol2/1000/vip套图 /vol2/1000/organized --verbose
        """
    )

    parser.add_argument('source_dir', help='源目录路径')
    parser.add_argument('target_dir', help='目标目录路径')
    parser.add_argument('--dry-run', action='store_true', help='试运行模式，不实际移动文件')
    parser.add_argument('--verbose', action='store_true', help='详细输出模式')

    args = parser.parse_args()

    # 验证参数
    source_path = Path(args.source_dir)
    if not source_path.exists():
        print(f"❌ 源目录不存在: {source_path}")
        sys.exit(1)

    if not source_path.is_dir():
        print(f"❌ 源路径不是目录: {source_path}")
        sys.exit(1)

    print("🤖 智能文件整理器")
    print("=" * 50)
    print(f"源目录: {source_path}")
    print(f"目标目录: {args.target_dir}")
    print(f"模式: {'试运行' if args.dry_run else '实际执行'}")
    print("=" * 50)

    try:
        # 创建智能整理器
        log_level = logging.DEBUG if args.verbose else logging.INFO
        organizer = SmartFileOrganizer(args.source_dir, args.target_dir, log_level)

        # 运行整理
        success = organizer.run(args.dry_run)

        if success:
            print("\n✅ 智能文件整理完成!")
            if args.dry_run:
                print("💡 这是试运行模式，没有实际移动文件。")
                print("   如果结果满意，请移除 --dry-run 参数重新运行。")
        else:
            print("\n❌ 智能文件整理失败!")
            sys.exit(1)

    except KeyboardInterrupt:
        print("\n⚠️ 操作被用户中断!")
    except Exception as e:
        print(f"\n❌ 发生错误: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
