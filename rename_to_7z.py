#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
文件后缀重命名工具
将指定文件夹中二级子目录的非压缩、非媒体文件后缀修改为7z

使用示例:
python3 rename_to_7z.py /vol2/1000/vip套图/整合
"""

import os
import sys
import argparse
from pathlib import Path


def rename_files_to_7z(directory, dry_run=False):
    """
    将指定文件夹中二级子目录的非压缩、非媒体文件后缀修改为7z
    
    Args:
        directory: 要处理的根目录路径
        dry_run: 是否为试运行模式，不实际重命名文件
    """
    print(f"📂 正在扫描目录: {directory}")
    if dry_run:
        print("🔍 试运行模式 - 不会实际修改文件")
    
    # 定义不需要修改的文件类型
    # 压缩格式
    archive_extensions = {
        '.zip', '.rar', '.7z', '.tar', '.gz', '.bz2', '.xz', 
        '.tar.gz', '.tar.bz2', '.tar.xz', '.tgz', '.tbz2', 
        '.txz', '.lzma', '.Z', '.cab', '.ace', '.arj', '.lzh'
    }
    
    # 图片格式
    image_extensions = {
        '.jpg', '.jpeg', '.png', '.gif', '.bmp', '.tiff', '.tif', 
        '.webp', '.svg', '.ico', '.raw', '.cr2', '.nef', '.arw',
        '.dng', '.orf', '.rw2', '.pef', '.srw', '.x3f', '.heic', '.heif'
    }
    
    # 视频格式
    video_extensions = {
        '.mp4', '.avi', '.mkv', '.mov', '.wmv', '.flv', '.webm', 
        '.m4v', '.3gp', '.mpg', '.mpeg', '.ts', '.vob', '.asf',
        '.rm', '.rmvb', '.f4v', '.ogv', '.divx', '.xvid'
    }
    
    # 音频格式
    audio_extensions = {
        '.mp3', '.wav', '.flac', '.aac', '.ogg', '.wma', '.m4a', 
        '.opus', '.ape', '.ac3', '.dts', '.amr', '.au', '.aiff'
    }
    
    # 合并所有不需要修改的扩展名
    skip_extensions = archive_extensions | image_extensions | video_extensions | audio_extensions
    
    root_path = Path(directory)
    if not root_path.exists():
        print(f"❌ 错误: 目录不存在 - {directory}")
        return False
    
    if not root_path.is_dir():
        print(f"❌ 错误: 路径不是目录 - {directory}")
        return False
    
    # 扫描二级子目录
    processed_count = 0
    skipped_count = 0
    error_count = 0
    
    print(f"🔍 开始扫描二级子目录...")
    
    for first_level in root_path.iterdir():
        if not first_level.is_dir():
            continue

        print(f"\n📁 扫描一级目录: {first_level.name}")

        # 处理一级目录中的文件（这些文件在二级子目录中）
        files_in_dir = list(first_level.iterdir())
        file_count = len([f for f in files_in_dir if f.is_file()])

        if file_count == 0:
            print(f"  ℹ️ 目录为空，跳过")
            continue

        print(f"  🔍 找到 {file_count} 个文件")

        for file_path in files_in_dir:
            if not file_path.is_file():
                continue

            # 获取文件扩展名
            file_extension = file_path.suffix.lower()

            # 跳过不需要修改的文件类型
            if file_extension in skip_extensions:
                print(f"  ⚠️ 跳过 (媒体/压缩文件): {file_path.name}")
                skipped_count += 1
                continue

            # 跳过已经是.7z的文件
            if file_extension == '.7z':
                print(f"  ⚠️ 跳过 (已是7z格式): {file_path.name}")
                skipped_count += 1
                continue

            # 跳过没有扩展名的文件
            if not file_extension:
                print(f"  ⚠️ 跳过 (无扩展名): {file_path.name}")
                skipped_count += 1
                continue

            # 构造新的文件名 - 添加.7z后缀而不是替换
            new_file_path = file_path.with_suffix(file_path.suffix + '.7z')

            try:
                # 检查目标文件是否已存在
                if new_file_path.exists():
                    print(f"  ⚠️ 跳过 (目标文件已存在): {file_path.name} -> {new_file_path.name}")
                    skipped_count += 1
                    continue

                if dry_run:
                    print(f"  🔄 [试运行] 将重命名: {file_path.name} -> {new_file_path.name}")
                    processed_count += 1
                else:
                    # 重命名文件
                    file_path.rename(new_file_path)
                    print(f"  ✅ 重命名: {file_path.name} -> {new_file_path.name}")
                    processed_count += 1

            except Exception as e:
                print(f"  ❌ 重命名失败: {file_path.name} - {e}")
                error_count += 1
    
    # 打印统计信息
    mode_text = "试运行" if dry_run else "实际执行"
    print(f"\n📊 处理完成 ({mode_text}):")
    print(f"  ✅ {'将要' if dry_run else '成功'}重命名: {processed_count} 个文件")
    print(f"  ⚠️ 跳过文件: {skipped_count} 个文件")
    if error_count > 0:
        print(f"  ❌ 错误: {error_count} 个文件")
    
    return error_count == 0


def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description="文件后缀重命名工具 - 将二级子目录中的非压缩、非媒体文件后缀修改为7z",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  # 试运行模式，查看将要修改的文件
  python3 rename_to_7z.py /vol2/1000/vip套图/整合 --dry-run
  
  # 实际执行重命名
  python3 rename_to_7z.py /vol2/1000/vip套图/整合
  
说明:
  - 只处理指定目录的二级子目录中的文件
  - 不会修改压缩文件 (.zip, .rar, .7z 等)
  - 不会修改图片文件 (.jpg, .png, .gif 等)
  - 不会修改视频文件 (.mp4, .avi, .mkv 等)
  - 不会修改音频文件 (.mp3, .wav, .flac 等)
        """
    )
    
    parser.add_argument('directory', help='要处理的根目录路径')
    parser.add_argument('--dry-run', action='store_true', help='试运行模式，不实际修改文件')
    
    args = parser.parse_args()
    
    if not os.path.isdir(args.directory):
        print(f"❌ 错误: 目录不存在 - {args.directory}")
        sys.exit(1)
    
    print("🔧 文件后缀重命名工具")
    print("=" * 50)
    print(f"目标目录: {args.directory}")
    print(f"模式: {'试运行' if args.dry_run else '实际执行'}")
    print("=" * 50)
    
    try:
        success = rename_files_to_7z(args.directory, args.dry_run)
        
        if success:
            print("\n🎉 操作完成!")
            if args.dry_run:
                print("💡 这是试运行模式，没有实际修改文件。")
                print("   如果结果满意，请移除 --dry-run 参数重新运行。")
        else:
            print("\n❌ 操作过程中出现错误!")
            sys.exit(1)
            
    except KeyboardInterrupt:
        print("\n⚠️ 操作被用户中断!")
    except Exception as e:
        print(f"\n❌ 发生错误: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
